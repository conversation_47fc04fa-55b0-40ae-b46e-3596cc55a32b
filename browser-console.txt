
🧹 Clearing app cache... app.js:3208:11
🗑️ Removed localStorage key: byevape-backup app.js:3215:15
🗑️ Removed localStorage key: byevape-backup-2025-06-26 app.js:3215:15
🗑️ Removed localStorage key: byevape-userdata app.js:3215:15
🗑️ Removed localStorage key: byevape-launched app.js:3215:15
[vite] connecting... client:495:8
[vite] connected. client:618:14
📝 Logger initialized with level: debug logger.js:13:12
ℹ️ [INFO] 🏗️ AppState constructor called 
Object {  }
logger.js:106:12
ℹ️ Running in browser mode - SplashScreen not available app.js:156:12
🔍 Detecting environment... 
Object { hasCapacitor: false, hasCapacitorPlugins: false, readyState: "interactive", userAgent: "Browser" }
app.js:3866:8
🌐 Running in web browser app.js:3895:10
🚀 Initializing ByeVape app with modular architecture... app.js:3824:12
🔄 Showing splash screen... app.js:3705:12
ℹ️ Running in browser - no splash screen to show app.js:3713:14
🚀 ByeVape main entry point loaded main.js:18:8
ℹ️ [INFO] 🔄 Initializing AppState... [Modular Version: 2025.01.27.002] 
Object {  }
logger.js:106:12
📦 AppState build timestamp: 2025-06-26T10:16:27.664Z appState.js:41:14
🔧 Modular cache buster: nwk3oaypv appState.js:42:14
ℹ️ [INFO] 🛡️ ErrorHandler initialized 
Object {  }
logger.js:106:12
Folgende nicht unterstützte entryTypes werden ignoriert: longtask. errorHandler.js:77:17
Keine gültigen entryTypes; Registrierung wird abgebrochen. errorHandler.js:77:17
ℹ️ [INFO] ✅ ErrorHandler initialization complete 
Object {  }
logger.js:106:12
🔧 StateManager initialized stateManager.js:12:12
🔄 Initializing StateManager... stateManager.js:82:14
✅ StateManager initialized successfully stateManager.js:102:14
🎨 UIRenderer initialized uiRenderer.js:12:12
🔄 Initializing UIRenderer... uiRenderer.js:20:14
⚠️ Element not found: current-day uiRenderer.js:65:16
⚠️ Element not found: days-remaining uiRenderer.js:65:16
⚠️ Element not found: progress-percentage uiRenderer.js:65:16
📦 Cached 9 DOM elements uiRenderer.js:69:12
🎨 Rendering all UI elements... uiRenderer.js:137:14
🔄 Progress updated: 0.0% (0/0) 2 uiRenderer.js:261:14
✅ All UI elements rendered uiRenderer.js:146:14
✅ UIRenderer initialized successfully uiRenderer.js:32:14
📊 TrackerLogic initialized trackerLogic.js:11:12
🔄 Initializing TrackerLogic... trackerLogic.js:19:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
🔄 State updated: userData.dailyPuffTarget = 0 stateManager.js:307:14
🎯 Daily puff target updated: 0 puffs trackerLogic.js:74:14
✅ TrackerLogic initialized successfully trackerLogic.js:28:14
🔗 Module communication setup complete appState.js:106:14
🔄 Performing daily reset... trackerLogic.js:306:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
📊 State change: userData.totalPuffsToday = 0 (was: 0) appState.js:169:16
⏰ Puff pace check: 6.3h elapsed, expected: 0.0, actual: 0 trackerLogic.js:118:14
📊 Puff rate: 0.00/h (target: 0.00/h), usage: 0.0% trackerLogic.js:119:14
🔄 State updated: ui.statusMessage = 🎉 Ausgezeichnet! Sie haben heute 6.3 Stunden ohne Puffs verbracht. stateManager.js:307:14
🔄 State updated: ui.statusType = great stateManager.js:307:14
🔄 State updated: ui.showStatusMessage = true stateManager.js:307:14
🔄 State updated: app.currentStats = [object Object] stateManager.js:307:14
🔄 State updated: userData.totalPuffsToday = 0 stateManager.js:307:14
🔄 State updated: userData.puffTimes = stateManager.js:307:14
🔄 State updated: userData.lastPuffTime = null stateManager.js:307:14
🔄 State updated: userData.lastResetDate = 2025-06-26 stateManager.js:307:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
📊 State change: userData.dailyPuffTarget = 0 (was: 0) appState.js:169:16
🎯 Daily target changed: 0 → 0 appState.js:211:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
🔄 State updated: userData.dailyPuffTarget = 0 stateManager.js:307:14
🎯 Daily puff target updated: 0 puffs trackerLogic.js:74:14
🔄 State updated: userData.currentDay = 2 stateManager.js:307:14
📦 Enhanced data backup created stateManager.js:524:14
✅ User data saved to localStorage stateManager.js:346:16
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
✅ Daily reset completed for day 2 trackerLogic.js:324:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
📊 State change: userData.dailyPuffTarget = 0 (was: 0) appState.js:169:16
🎯 Daily target changed: 0 → 0 appState.js:211:14
🔄 Progress updated: 0.0% (0/0) uiRenderer.js:261:14
🔄 State updated: userData.dailyPuffTarget = 0 stateManager.js:307:14
🎯 Daily puff target updated: 0 puffs trackerLogic.js:74:14
🎨 Rendering all UI elements... uiRenderer.js:137:14
🔄 Progress updated: 0.0% (0/0) 2 uiRenderer.js:261:14
✅ All UI elements rendered uiRenderer.js:146:14
✅ Initial checks completed appState.js:153:14
ℹ️ [INFO] ✅ AppState initialized successfully 
Object {  }
logger.js:106:12
🔍 [DEBUG] AppState initialization completed in 3ms 
Object { duration: 3, operation: "AppState initialization" }
logger.js:94:12
✅ ByeVape app initialized successfully with modular architecture app.js:3833:14
Initializing ByeVape AppState... app.js:212:14
User data loaded successfully app.js:236:16
No daily history found app.js:247:16
🔍 First launch check: hasLaunched=false, hasUserData=false app.js:1439:14
🚀 ByeVape app starting... [Version: 2025.01.27.004] app.js:1455:12
📦 Build timestamp: 2025-06-26T10:16:27.667Z app.js:1456:12
🔧 Environment: https://localhost:3000 app.js:1457:12
💾 Cache buster: jipm2hppu app.js:1458:12
🌐 User agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0 app.js:1459:12
📱 Platform: Browser app.js:1460:12
🔄 State updated: ui.statusMessage = stateManager.js:307:14

​

