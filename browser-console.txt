
📝 Logger initialized with level: debug index-DD7peCTk.js:44:13
🔄 State updated: app.currentStats = [object Object] index-DD7peCTk.js:910:15
🔄 State updated: userData.totalPuffsToday = 35 index-DD7peCTk.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:53:25.371Z index-DD7peCTk.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z index-DD7peCTk.js:910:15
📦 Enhanced data backup created index-DD7peCTk.js:1067:15
✅ User data saved to localStorage index-DD7peCTk.js:936:17
🔄 Progress updated: 0.0% (35/0) index-DD7peCTk.js:1486:15
ℹ️ [INFO] 📝 Logged 5 puff(s). Total: 35 
Object {  }
index-DD7peCTk.js:120:13
✅ Quick added 5 puff(s) index-DD7peCTk.js:4984:17
🔍 [DEBUG] 🔄 Global logPuffs called 
Object {  }
index-DD7peCTk.js:111:13
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "logPuffs", count: "1" }
index-DD7peCTk.js:111:13
ℹ️ [INFO] User action: logPuffs 
Object { action: "logPuffs", details: {…}, timestamp: 1750931606733 }
index-DD7peCTk.js:120:13
🔄 Progress updated: 0.0% (36/0) index-DD7peCTk.js:1486:15
📊 State change: userData.totalPuffsToday = 36 (was: 35) index-DD7peCTk.js:2149:17
⏰ Puff pace check: 5.9h elapsed, expected: 0.0, actual: 36 index-DD7peCTk.js:1775:15
📊 Puff rate: 6.11/h (target: 0.00/h), usage: 0.0% index-DD7peCTk.js:1776:15
🔄 State updated: ui.statusMessage = ⚠️ Sie sind deutlich vor Ihrem Puff-Tempo. -36 Puffs verbleiben für 12.1 Stunden. index-DD7peCTk.js:910:15
🔄 State updated: ui.statusType = warning index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = true index-DD7peCTk.js:910:15
🔄 State updated: app.currentStats = [object Object] index-DD7peCTk.js:910:15
🔄 State updated: userData.totalPuffsToday = 36 index-DD7peCTk.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:53:26.734Z index-DD7peCTk.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:26.734Z index-DD7peCTk.js:910:15
📦 Enhanced data backup created index-DD7peCTk.js:1067:15
✅ User data saved to localStorage index-DD7peCTk.js:936:17
🔄 Progress updated: 0.0% (36/0) index-DD7peCTk.js:1486:15
ℹ️ [INFO] 📝 Logged 1 puff(s). Total: 36 
Object {  }
index-DD7peCTk.js:120:13
🔍 [DEBUG] Retry attempt 3/3 
Object { operation: "undoLastPuff" }
index-DD7peCTk.js:111:13
ℹ️ [INFO] User action: undoLastPuff 
Object { action: "undoLastPuff", details: {}, timestamp: 1750931606986 }
index-DD7peCTk.js:120:13
🔄 State updated: ui.statusMessage = No actions to undo today. index-DD7peCTk.js:910:15
🔄 State updated: ui.statusType = info index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = true index-DD7peCTk.js:910:15
⚠️ [WARN] Attempt 3 failed Error: No actions to undo today.
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2356
    retry https://localhost:3001/assets/index-DD7peCTk.js:615
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2350
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:5017
    onclick https://localhost:3001/:1
index-DD7peCTk.js:129:13
❌ [ERROR] All retry attempts failed Error: No actions to undo today.
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2356
    retry https://localhost:3001/assets/index-DD7peCTk.js:615
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2350
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:5017
    onclick https://localhost:3001/:1
 
Object { operation: "undoLastPuff" }
index-DD7peCTk.js:138:13
❌ [ERROR] ❌ Error undoing last puff Error: No actions to undo today.
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2356
    retry https://localhost:3001/assets/index-DD7peCTk.js:615
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:2350
    undoLastPuff https://localhost:3001/assets/index-DD7peCTk.js:5017
    onclick https://localhost:3001/:1
 
Object {  }
index-DD7peCTk.js:138:13
🔄 State updated: ui.statusMessage = Error undoing action. index-DD7peCTk.js:910:15
🔄 State updated: ui.statusType = error index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = true index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15
🔄 Global quickAddPuffs called with count: 10 index-DD7peCTk.js:4969:11
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "logPuffs", count: 10 }
index-DD7peCTk.js:111:13
ℹ️ [INFO] User action: logPuffs 
Object { action: "logPuffs", details: {…}, timestamp: 1750931613297 }
index-DD7peCTk.js:120:13
🔄 Progress updated: 0.0% (46/0) index-DD7peCTk.js:1486:15
📊 State change: userData.totalPuffsToday = 46 (was: 36) index-DD7peCTk.js:2149:17
⏰ Puff pace check: 5.9h elapsed, expected: 0.0, actual: 46 index-DD7peCTk.js:1775:15
📊 Puff rate: 7.81/h (target: 0.00/h), usage: 0.0% index-DD7peCTk.js:1776:15
🔄 State updated: ui.statusMessage = ⚠️ Sie sind deutlich vor Ihrem Puff-Tempo. -46 Puffs verbleiben für 12.1 Stunden. index-DD7peCTk.js:910:15
🔄 State updated: ui.statusType = warning index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = true index-DD7peCTk.js:910:15
🔄 State updated: app.currentStats = [object Object] index-DD7peCTk.js:910:15
🔄 State updated: userData.totalPuffsToday = 46 index-DD7peCTk.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:53:33.298Z index-DD7peCTk.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:26.734Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z index-DD7peCTk.js:910:15
📦 Enhanced data backup created index-DD7peCTk.js:1067:15
✅ User data saved to localStorage index-DD7peCTk.js:936:17
🔄 Progress updated: 0.0% (46/0) index-DD7peCTk.js:1486:15
ℹ️ [INFO] 📝 Logged 10 puff(s). Total: 46 
Object {  }
index-DD7peCTk.js:120:13
✅ Quick added 10 puff(s) index-DD7peCTk.js:4984:17
🔄 Global quickAddPuffs called with count: 10 index-DD7peCTk.js:4969:11
🔍 [DEBUG] Retry attempt 1/3 
Object { operation: "logPuffs", count: 10 }
index-DD7peCTk.js:111:13
ℹ️ [INFO] User action: logPuffs 
Object { action: "logPuffs", details: {…}, timestamp: 1750931614529 }
index-DD7peCTk.js:120:13
🔄 Progress updated: 0.0% (56/0) index-DD7peCTk.js:1486:15
📊 State change: userData.totalPuffsToday = 56 (was: 46) index-DD7peCTk.js:2149:17
⏰ Puff pace check: 5.9h elapsed, expected: 0.0, actual: 56 index-DD7peCTk.js:1775:15
📊 Puff rate: 9.50/h (target: 0.00/h), usage: 0.0% index-DD7peCTk.js:1776:15
🔄 State updated: ui.statusMessage = ⚠️ Sie sind deutlich vor Ihrem Puff-Tempo. -56 Puffs verbleiben für 12.1 Stunden. index-DD7peCTk.js:910:15
🔄 State updated: ui.statusType = warning index-DD7peCTk.js:910:15
🔄 State updated: ui.showStatusMessage = true index-DD7peCTk.js:910:15
🔄 State updated: app.currentStats = [object Object] index-DD7peCTk.js:910:15
🔄 State updated: userData.totalPuffsToday = 56 index-DD7peCTk.js:910:15
🔄 State updated: userData.lastPuffTime = 2025-06-26T09:53:34.529Z index-DD7peCTk.js:910:15
🔄 State updated: userData.puffTimes = 2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:03.622Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:14.616Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:22.375Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:25.371Z,2025-06-26T09:53:26.734Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:33.298Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z,2025-06-26T09:53:34.529Z index-DD7peCTk.js:910:15
📦 Enhanced data backup created index-DD7peCTk.js:1067:15
✅ User data saved to localStorage index-DD7peCTk.js:936:17
🔄 Progress updated: 0.0% (56/0) index-DD7peCTk.js:1486:15
ℹ️ [INFO] 📝 Logged 10 puff(s). Total: 56 
Object {  }
index-DD7peCTk.js:120:13
✅ Quick added 10 puff(s) index-DD7peCTk.js:4984:17
🔄 State updated: ui.showStatusMessage = false index-DD7peCTk.js:910:15
🔄 State updated: ui.statusMessage = index-DD7peCTk.js:910:15

​

