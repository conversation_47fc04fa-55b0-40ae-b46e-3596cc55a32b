import { defineConfig } from 'vite';
import basicSsl from '@vitejs/plugin-basic-ssl';

const useHttps = process.env.VITE_HTTPS === 'true';

export default defineConfig({
  plugins: useHttps ? [basicSsl()] : [],
  root: './src',
  build: {
    outDir: '../dist',
    minify: false,
    emptyOutDir: true,
  },
  server: {
    host: true,
    port: 3000,
    open: false
  },
  preview: {
    host: true,
    port: 3001,
    open: false
  }
});
